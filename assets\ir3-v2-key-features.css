/* IR3-V2 Key Features CSS - Premium Design */

.key-features-section {
  position: relative;
  width: 100%;
  background:
    radial-gradient(circle at 25% 25%, rgba(255, 149, 0, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 95, 109, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 50% 10%, rgba(66, 165, 245, 0.06) 0%, transparent 60%),
    linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 30%, #0f0f0f 70%, #080808 100%);
  color: #ffffff;
  padding: 40px 0;
  height: 100vh;
  min-height: 800px;
  display: flex;
  align-items: center;
  justify-content: center;

  box-sizing: border-box;
  margin-top: 0 !important;
}

/* 🔧 桌面端专用：overflow hidden 仅在桌面端生效 */
@media screen and (min-width: 769px) {
  .key-features-section {
    overflow: hidden;
  }
}

/* Background Layers */
.key-features-section .background-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}

.key-features-section .gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #080808;
  z-index: 2;
}

.key-features-section .grid-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 30px 30px;
  z-index: 3;
}

.key-features-section .tech-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    linear-gradient(90deg, transparent 49.7%, rgba(255, 149, 0, 0.05) 50%, transparent 50.3%),
    linear-gradient(0deg, transparent 49.7%, rgba(255, 95, 109, 0.05) 50%, transparent 50.3%);
  background-size: 120px 120px;
  opacity: 0.6;
  z-index: 4;
}

/* 性能优化：简化浮动形状动画 */
.key-features-section .floating-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  overflow: hidden;
  pointer-events: none;
}

.key-features-section .shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 149, 0, 0.05), rgba(255, 95, 109, 0.03)); /* 降低透明度 */
  filter: blur(20px); /* 减少模糊半径 */
  opacity: 0.2; /* 降低透明度 */
  will-change: transform;
}

.key-features-section .shape-1 {
  width: 250px; /* 减小尺寸 */
  height: 250px;
  top: 15%;
  left: 10%;
  animation: float-slow 30s infinite ease-in-out alternate; /* 减慢动画 */
}

.key-features-section .shape-2 {
  width: 300px; /* 减小尺寸 */
  height: 300px;
  bottom: 10%;
  right: 5%;
  background: linear-gradient(135deg, rgba(255, 95, 109, 0.03), rgba(255, 149, 0, 0.05)); /* 降低透明度 */
  animation: float-slow 35s infinite ease-in-out alternate-reverse; /* 减慢动画 */
}

.key-features-section .shape-3 {
  width: 150px; /* 减小尺寸 */
  height: 150px;
  top: 45%;
  right: 25%;
  background: radial-gradient(circle, rgba(255, 149, 0, 0.05) 0%, rgba(255, 95, 109, 0.03) 100%); /* 降低透明度 */
  animation: float-slow 40s infinite ease-in-out alternate; /* 减慢动画 */
}

/* 性能优化：简化浮动动画 */
@keyframes float-slow {
  0%, 100% {
    transform: translate3d(0, 0, 0) scale(1);
  }
  50% {
    transform: translate3d(-20px, 20px, 0) scale(1.02); /* 减少移动距离和缩放 */
  }
}

/* Content Container */
.key-features-section .features-container {
  position: relative;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  z-index: 10;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Title Group */
.key-features-section .title-group {
  text-align: center;
  margin-bottom: 40px;
  margin-top: -280px;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}

.key-features-section .main-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 54px;
  font-weight: 900;
  letter-spacing: -0.5px;
  margin-bottom: 24px;
  position: relative;
  display: inline-block;
  background: linear-gradient(135deg, #fff 0%, #42a5f5 50%, #1de9b6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 40px rgba(66, 165, 245, 0.3);
  position: relative;
}

/* Glitch effect similar to hero section */
.key-features-section .main-title::before,
.key-features-section .main-title::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #fff 0%, #42a5f5 50%, #1de9b6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.key-features-section .main-title::before {
  animation: glitch-1 0.5s infinite;
  color: #42a5f5;
  z-index: -1;
}

.key-features-section .main-title::after {
  animation: glitch-2 0.5s infinite;
  color: #1de9b6;
  z-index: -2;
}

@keyframes glitch-1 {
  0%, 100% { clip-path: inset(0 0 0 0); transform: translate(0); }
  20% { clip-path: inset(0 100% 0 0); transform: translate(-2px); }
  40% { clip-path: inset(0 0 0 100%); transform: translate(2px); }
  60% { clip-path: inset(100% 0 0 0); transform: translate(-1px); }
  80% { clip-path: inset(0 0 100% 0); transform: translate(1px); }
}

@keyframes glitch-2 {
  0%, 100% { clip-path: inset(0 0 0 0); transform: translate(0); }
  20% { clip-path: inset(100% 0 0 0); transform: translate(1px); }
  40% { clip-path: inset(0 0 100% 0); transform: translate(-1px); }
  60% { clip-path: inset(0 100% 0 0); transform: translate(2px); }
  80% { clip-path: inset(0 0 0 100%); transform: translate(-2px); }
}

.key-features-section .title-underline {
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #ffffff 0%, rgba(255, 255, 255, 0.2) 100%);
  margin: 0 auto 24px;
  border-radius: 2px;
}

.key-features-section .subtitle {
  font-family: 'Open Sans', sans-serif;
  font-size: 20px;
  font-weight: 300;
  line-height: 1.6;
  max-width: 900px;
  margin: 0 auto;
  padding: 0 20px;
  color: rgba(255, 255, 255, 0.9);
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.95) 0%, rgba(66, 165, 245, 0.8) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.02em;
}

/* Feature Showcase */
.key-features-section .feature-showcase {
  display: grid;
  grid-template-columns: 80px 1fr 80px;
  align-items: center;
  gap: 30px;
  margin-bottom: 20px;
  position: relative;
  z-index: 5;
}

/* Feature Content Wrap */
.key-features-section .feature-content-wrap {
  display: grid;
  grid-template-columns: 1.2fr 0.8fr;
  gap: 50px;
  align-items: center;
  position: relative;
  max-width: 1300px;
  margin: 0 auto;
}

/* Feature Navigation Buttons */
.key-features-section .feature-nav-button {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px);
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.3), inset 0 1px 2px rgba(255, 255, 255, 0.1);
  z-index: 20;
}

.key-features-section .feature-nav-button::after {
  content: '';
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  height: 40%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), transparent);
  border-radius: 50% 50% 0 0;
  opacity: 0.5;
}

.key-features-section .feature-nav-button:hover {
  color: #ffffff;
  transform: scale(1.05);
}

.key-features-section .feature-nav-button .button-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  z-index: -1;
  transform: scale(0);
  transition: transform 0.4s cubic-bezier(0.215, 0.610, 0.355, 1.000);
}

.key-features-section .feature-nav-button:hover .button-bg {
  transform: scale(1);
}

.key-features-section .feature-nav-button svg {
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease;
}

.key-features-section .prev-button:hover svg {
  transform: translateX(-2px);
}

.key-features-section .next-button:hover svg {
  transform: translateX(2px);
}

/* Feature Images */
.key-features-section .feature-image-container {
  position: relative;
  width: 100%;
  overflow: visible;
  border-radius: 8px;
  margin-bottom: 10px;
  transform-style: preserve-3d;
  perspective: 1200px;
  height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.key-features-section .feature-images {
  position: relative;
  width: 100%;
  aspect-ratio: 16/10;
  perspective: 1200px;
  transform-style: preserve-3d;
  display: flex;
  justify-content: center;
  align-items: center;
}

.key-features-section .feature-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 150%;
  height: 150%;
  object-fit: contain;
  opacity: 0;
  transform: scale(0) translateZ(0);
  transition: opacity 1.0s ease, transform 1.0s ease;
  border-radius: 8px;
  background: transparent;
  will-change: transform, opacity;
}

.key-features-section .feature-img.prev {
  opacity: 0.15 !important;
  transform: translateX(-30%) scale(0.7) rotateY(-15deg) translateZ(-100px) !important;
  filter: blur(3px) brightness(0.6) !important;
  z-index: 2 !important;
  visibility: visible !important;
}

.key-features-section .feature-img.next {
  opacity: 0.15 !important;
  transform: translateX(30%) scale(0.7) rotateY(15deg) translateZ(-100px) !important;
  filter: blur(3px) brightness(0.6) !important;
  z-index: 2 !important;
  visibility: visible !important;
}

/* 移除蓝色光效果以提升性能和视觉效果 */
/* .key-features-section .feature-image-container::after - 已移除 */

.key-features-section .feature-img.active {
  opacity: 1 !important;
  transform: scale(1.3) translateX(0) translateZ(0) rotateY(0) !important;
  filter: blur(0) brightness(1) !important;
  z-index: 5 !important;
  visibility: visible !important;
}

/* Feature Text Container */
.key-features-section .feature-text-container {
  position: relative;
  width: 100%;
  z-index: 6; /* Ensure text is above images */
}

.key-features-section .feature-texts {
  position: relative;
}

.key-features-section .feature-text {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: opacity 1.0s ease, transform 1.0s ease, visibility 1.0s ease;
  padding-right: 20px;
}

.key-features-section .feature-text.active {
  position: relative;
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.key-features-section .feature-number {
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  font-weight: 700;
  background: linear-gradient(90deg, #FF9500 0%, #FF5F6D 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 18px;
  display: block;
  letter-spacing: 1px;
  opacity: 0.8;
}

.key-features-section .feature-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 40px;
  font-weight: 800;
  margin-bottom: 24px;
  background: linear-gradient(135deg, #FF9500 0%, #FF5F6D 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  letter-spacing: -0.5px;
  line-height: 1.2;
  text-shadow: 0 0 20px rgba(255, 95, 109, 0.3);
}

.key-features-section .feature-title:after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 0;
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, rgba(255, 149, 0, 0.9) 0%, rgba(255, 95, 109, 0.6) 100%);
  border-radius: 2px;
  box-shadow: 0 0 15px rgba(255, 95, 109, 0.5);
  animation: pulseWidth 4s infinite alternate ease-in-out;
}

@keyframes pulseWidth {
  0% { width: 100px; opacity: 0.9; }
  100% { width: 150px; opacity: 0.7; }
}

.key-features-section .feature-description {
  font-family: 'Open Sans', sans-serif;
  font-size: 18px;
  line-height: 1.7;
  margin-bottom: 30px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.95) 0%, rgba(66, 165, 245, 0.7) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.01em;
}

.key-features-section .feature-bullets {
  list-style: none;
  padding: 0;
  margin: 30px 0 0;
}

.key-features-section .feature-bullets li {
  position: relative;
  padding-left: 32px;
  margin-bottom: 16px;
  font-size: 16px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.9) 0%, rgba(29, 233, 182, 0.7) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  align-items: center;
}

.key-features-section .feature-bullets li:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  background-color: rgba(255, 149, 0, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
  border: 1px solid rgba(255, 95, 109, 0.2);
}

.key-features-section .feature-bullets li:after {
  content: '';
  position: absolute;
  left: 7px;
  top: 50%;
  width: 6px;
  height: 10px;
  border: solid #FF5F6D;
  border-width: 0 2px 2px 0;
  transform: translateY(-60%) rotate(45deg);
  transition: transform 0.3s ease;
  box-shadow: 1px 1px 0px rgba(255, 149, 0, 0.5);
}

.key-features-section .feature-bullets li:hover:before {
  background-color: rgba(255, 149, 0, 0.2);
  box-shadow: 0 0 8px rgba(255, 95, 109, 0.3);
}

.key-features-section .feature-bullets li:hover:after {
  transform: translateY(-60%) rotate(45deg) scale(1.1);
}



/* Feature Tags */
.key-features-section .feature-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  margin-bottom: 60px;
  margin-top: -10px;
  position: relative;
  z-index: 25;
}

.key-features-section .feature-tag {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 25px;
  padding: 14px 32px;
  font-family: 'Montserrat', sans-serif;
  font-size: 15px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48px;
  min-width: 180px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(15px);
}

.key-features-section .feature-tag:hover,
.key-features-section .feature-tag.active {
  color: #ffffff;
  border-color: rgba(255, 149, 0, 0.6);
  background: rgba(255, 149, 0, 0.1);
  box-shadow: 0 6px 20px rgba(255, 149, 0, 0.3);
  transform: translateY(-2px);
}

.key-features-section .feature-tag .tag-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.05);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.key-features-section .feature-tag:hover .tag-bg {
  opacity: 1;
}

.key-features-section .feature-tag.active .tag-bg {
  opacity: 1;
  background: linear-gradient(135deg, rgba(255, 149, 0, 0.2), rgba(255, 95, 109, 0.2));
}

/* Responsive Styles */

/* 🖥️ 中等屏幕适配 (1200px-1920px) - 针对1920*911等中等分辨率 */
@media screen and (max-width: 1920px) and (min-width: 1200px) {
  .key-features-section {
    padding: 40px 0;
    min-height: 100vh;
    height: auto; /* 改为自适应高度 */
    display: flex;
    align-items: center;
  }

  .key-features-section .features-container {
    max-width: 95%;
    padding: 0 1.5rem;
  }

  .key-features-section .title-group {
    margin-bottom: 2rem; /* 减少底部间距 */
    margin-top: 0 !important; /* 移除负边距 */
    padding-top: 1rem; /* 减少顶部内边距 */
    max-width: 100%; /* 确保不会溢出 */
  }

  .key-features-section .main-title {
    font-size: 38px; /* 减小标题字体 */
    margin-bottom: 16px; /* 减少间距 */
    line-height: 1.1; /* 紧凑行高 */
  }

  .key-features-section .subtitle {
    font-size: 16px; /* 减小副标题字体 */
    max-width: 90%;
    margin: 0 auto;
    line-height: 1.5; /* 紧凑行高 */
    padding: 0 10px; /* 减少内边距 */
  }

  .key-features-section .feature-image-container {
    height: 280px; /* 减小图片容器高度 */
    margin-bottom: 1rem; /* 减少底部间距 */
  }

  .key-features-section .feature-img {
    width: 120%; /* 减小图片尺寸 */
    height: 120%;
    max-width: 120%;
    max-height: 120%;
  }

  .key-features-section .feature-img.active {
    transform: scale(1.1) translateX(0) translateZ(0) rotateY(0) !important; /* 减小活动图片缩放 */
  }

  .key-features-section .feature-content-wrap {
    gap: 1.5rem; /* 减少间距 */
    max-width: 100%;
    margin: 0 auto;
  }

  .key-features-section .feature-text-container {
    padding: 1rem; /* 减少内边距 */
  }

  .key-features-section .feature-title {
    font-size: 28px; /* 减小特性标题字体 */
    margin-bottom: 0.8rem; /* 减少间距 */
    line-height: 1.2;
  }

  .key-features-section .feature-description {
    font-size: 14px; /* 减小描述字体 */
    line-height: 1.5; /* 紧凑行高 */
    margin-bottom: 1.5rem; /* 减少间距 */
  }

  .key-features-section .feature-bullets {
    margin: 1.5rem 0 0; /* 减少间距 */
  }

  .key-features-section .feature-bullets li {
    font-size: 13px; /* 减小列表字体 */
    margin-bottom: 10px; /* 减少间距 */
  }

  .key-features-section .feature-tags {
    margin-bottom: 2rem; /* 减少间距 */
    margin-top: 0; /* 移除负边距 */
  }

  .key-features-section .feature-tag {
    padding: 10px 24px; /* 减小标签内边距 */
    font-size: 13px; /* 减小标签字体 */
    height: 40px; /* 减小标签高度 */
    min-width: 140px; /* 减小最小宽度 */
  }
}

/* 🖥️ 小屏幕适配 (768px-1200px) - 针对平板和小笔记本 */
@media screen and (max-width: 1200px) and (min-width: 769px) {
  .key-features-section {
    height: auto; /* 自适应高度 */
    min-height: 700px; /* 减小最小高度 */
    padding: 30px 0; /* 减少内边距 */
  }

  .key-features-section .title-group {
    margin-bottom: 1.5rem; /* 进一步减少间距 */
    margin-top: 0 !important;
  }

  .key-features-section .main-title {
    font-size: 32px; /* 进一步减小标题 */
    margin-bottom: 12px;
  }

  .key-features-section .subtitle {
    font-size: 14px; /* 进一步减小副标题 */
    line-height: 1.4;
  }

  .key-features-section .feature-content-wrap {
    grid-template-columns: 1fr;
    gap: 20px; /* 减少间距 */
  }

  .key-features-section .feature-text-container {
    order: -1;
    padding: 0.5rem; /* 减少内边距 */
  }

  .key-features-section .feature-title {
    font-size: 26px; /* 减小特性标题 */
    margin-bottom: 0.5rem;
  }

  .key-features-section .feature-description {
    font-size: 14px; /* 减小描述字体 */
    margin-bottom: 1rem;
  }

  .key-features-section .feature-image-container {
    height: 250px; /* 大幅减小图片容器高度 */
    margin-top: 0; /* 移除负边距 */
    margin-bottom: 1rem; /* 减少间距 */
  }

  .key-features-section .feature-img {
    width: 100%; /* 减小图片尺寸 */
    height: 100%;
  }

  .key-features-section .feature-img.active {
    transform: scale(1.0) translateX(0) translateZ(0) rotateY(0) !important; /* 不缩放 */
  }

  .key-features-section .feature-tags {
    margin-bottom: 1.5rem;
    gap: 12px;
  }

  .key-features-section .feature-tag {
    padding: 8px 16px;
    font-size: 12px;
    height: 36px;
    min-width: 120px;
  }
}

@media screen and (max-width: 768px) {
  .key-features-section {
    padding: 40px 0;
    height: auto;
    min-height: 700px;
    align-items: flex-start;
    /* 🔧 移动端滚动优化：移除 overflow-y 避免创建独立滚动上下文 */
    overflow: visible;
    position: relative;
  }
  
  .key-features-section .feature-showcase {
    grid-template-columns: 40px 1fr 40px;
    gap: 15px;
    order: -8;
  }
  
  .key-features-section .feature-nav-button {
    width: 40px;
    height: 40px;
  }
  
  .key-features-section .title-group {
    margin-bottom: 30px;
    margin-top: 0 !important;
    order: -10;
  }
  
  .key-features-section .main-title {
    font-size: 36px;
  }
  
  .key-features-section .subtitle {
    font-size: 16px;
  }
  
  .key-features-section .feature-title {
    font-size: 28px;
    margin-bottom: 16px;
  }
  
  .key-features-section .feature-description {
    font-size: 15px;
    margin-bottom: 20px;
  }
  
  .key-features-section .feature-bullets li {
    font-size: 14px;
    margin-bottom: 12px;
  }
  
  .key-features-section .feature-tags {
    gap: 8px;
    margin-bottom: 25px;
    margin-top: 10px;
    order: -9;
    /* 🔧 移动端单行布局控制：确保三个标签水平排列在一行 */
    flex-wrap: nowrap; /* 禁止换行，强制单行显示 */
    overflow-x: auto; /* 如果内容过宽，允许水平滚动 */
    -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
  }

  .key-features-section .feature-tag {
    padding: 8px 14px; /* 减少内边距以适应单行布局 */
    font-size: 11px; /* 减小字体以适应更紧凑的布局 */
    min-width: 100px; /* 减少最小宽度以适应三个标签 */
    height: 36px;
    /* 🔧 移动端标签优化：防止收缩确保可读性 */
    flex-shrink: 0; /* 防止标签被压缩 */
    white-space: nowrap; /* 防止文本换行 */
  }
}

@media screen and (max-width: 480px) {
  .key-features-section {
    padding: 30px 0;
    height: auto;
    min-height: 600px;
    align-items: flex-start;
  }
  
  .key-features-section .title-group {
    margin-top: 0 !important;
    margin-bottom: 25px;
    order: -10;
  }

  .key-features-section .main-title {
    font-size: 28px;
  }
  
  .key-features-section .feature-showcase {
    grid-template-columns: 30px 1fr 30px;
    gap: 10px;
  }
  
  .key-features-section .feature-nav-button {
    width: 30px;
    height: 30px;
  }
  
  .key-features-section .feature-nav-button svg {
    width: 16px;
    height: 16px;
  }
  
  .key-features-section .feature-title {
    font-size: 24px;
  }
  
  .key-features-section .feature-number {
    font-size: 14px;
  }
  
  .key-features-section .feature-bullets li {
    padding-left: 28px;
  }

  .key-features-section .feature-tags {
    gap: 4px; /* 进一步减少间距以适应超小屏幕 */
    /* 🔧 超小屏幕单行布局：确保在最小设备上也能单行显示 */
    flex-wrap: nowrap; /* 强制单行显示 */
    justify-content: space-between; /* 均匀分布三个标签 */
    padding: 0 10px; /* 添加左右内边距防止贴边 */
  }

  .key-features-section .feature-tag {
    padding: 6px 10px; /* 进一步减少内边距 */
    font-size: 10px; /* 减小字体以适应超小屏幕 */
    min-width: 80px; /* 减少最小宽度以适应三个标签 */
    height: 32px;
    /* 🔧 超小屏幕标签优化：最大化利用空间 */
    flex: 1; /* 让标签平均分配可用空间 */
    max-width: calc(33.333% - 3px); /* 确保三个标签能够并排显示 */
    flex-shrink: 0; /* 防止标签被过度压缩 */
  }
}

/* 3D Printing Inspired Background Effects */
.key-features-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 8px,
      rgba(255, 149, 0, 0.02) 8px,
      rgba(255, 149, 0, 0.02) 16px
    ),
    repeating-linear-gradient(
      -45deg,
      transparent,
      transparent 12px,
      rgba(255, 95, 109, 0.015) 12px,
      rgba(255, 95, 109, 0.015) 24px
    );
  animation: printingPattern 60s linear infinite;
  z-index: 1;
  pointer-events: none;
}

@keyframes printingPattern {
  0% { transform: translate(0, 0); }
  100% { transform: translate(32px, 48px); }
}

/* Enhanced tech lines for 3D printing theme */
.key-features-section .tech-lines {
  animation: techFlow 25s linear infinite;
}

@keyframes techFlow {
  0% { transform: translate(0, 0); }
  100% { transform: translate(120px, 120px); }
}

/* 性能优化：简化3D打印粒子效果 */
.key-features-section .printing-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

.key-features-section .printing-particles::before,
.key-features-section .printing-particles::after {
  content: '';
  position: absolute;
  width: 3px; /* 减小尺寸 */
  height: 3px;
  background: radial-gradient(circle, rgba(255, 149, 0, 0.4) 0%, transparent 60%); /* 降低透明度 */
  border-radius: 50%;
  animation: particleFloat 15s infinite linear; /* 减慢动画 */
  will-change: transform;
}

.key-features-section .printing-particles::before {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 18s; /* 减慢动画 */
}

.key-features-section .printing-particles::after {
  top: 60%;
  right: 15%;
  background: radial-gradient(circle, rgba(255, 95, 109, 0.4) 0%, transparent 60%); /* 降低透明度 */
  animation-delay: 6s; /* 增加延迟 */
  animation-duration: 20s; /* 减慢动画 */
}

/* 性能优化：简化粒子浮动动画 */
@keyframes particleFloat {
  0% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: 0;
  }
  20% {
    opacity: 0.6; /* 降低最大透明度 */
  }
  80% {
    opacity: 0.6;
  }
  100% {
    transform: translate3d(0, -80vh, 0) scale(0.7); /* 减少移动距离 */
    opacity: 0;
  }
}

/* Tech Circuits */
.key-features-section .tech-circuits {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 3;
}

.key-features-section .circuit {
  position: absolute;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(66, 165, 245, 0.3) 20%,
    rgba(66, 165, 245, 0.6) 50%,
    rgba(66, 165, 245, 0.3) 80%,
    transparent 100%);
  border-radius: 2px;
}

.key-features-section .circuit-1 {
  width: 200px;
  height: 2px;
  top: 25%;
  left: -200px;
  animation: circuitFlow1 15s infinite linear;
}

.key-features-section .circuit-2 {
  width: 150px;
  height: 2px;
  top: 70%;
  right: -150px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 149, 0, 0.3) 20%,
    rgba(255, 149, 0, 0.6) 50%,
    rgba(255, 149, 0, 0.3) 80%,
    transparent 100%);
  animation: circuitFlow2 18s infinite linear;
}

.key-features-section .circuit-3 {
  width: 2px;
  height: 180px;
  left: 80%;
  top: -180px;
  background: linear-gradient(0deg,
    transparent 0%,
    rgba(255, 95, 109, 0.3) 20%,
    rgba(255, 95, 109, 0.6) 50%,
    rgba(255, 95, 109, 0.3) 80%,
    transparent 100%);
  animation: circuitFlow3 20s infinite linear;
}

@keyframes circuitFlow1 {
  0% { left: -200px; }
  100% { left: 100%; }
}

@keyframes circuitFlow2 {
  0% { right: -150px; }
  100% { right: 100%; }
}

@keyframes circuitFlow3 {
  0% { top: -180px; }
  100% { top: 100%; }
}

/* Floating Icons */
.key-features-section .floating-icons {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 4;
}

/* 性能优化：简化浮动图标动画 */
.key-features-section .print-icon {
  position: absolute;
  font-size: 20px; /* 减小尺寸 */
  opacity: 0.2; /* 降低透明度 */
  filter: grayscale(1) brightness(1.2); /* 减少滤镜强度 */
  animation: iconFloat 35s infinite ease-in-out; /* 减慢动画 */
  will-change: transform;
}

.key-features-section .icon-1 {
  top: 15%;
  left: 5%;
  animation-delay: 0s;
  animation-duration: 40s; /* 减慢动画 */
}

.key-features-section .icon-2 {
  top: 80%;
  right: 8%;
  animation-delay: 10s; /* 增加延迟 */
  animation-duration: 38s; /* 减慢动画 */
}

.key-features-section .icon-3 {
  top: 40%;
  left: 3%;
  animation-delay: 20s; /* 增加延迟 */
  animation-duration: 42s; /* 减慢动画 */
}

.key-features-section .icon-4 {
  top: 60%;
  right: 5%;
  animation-delay: 30s; /* 增加延迟 */
  animation-duration: 45s; /* 减慢动画 */
}

/* 性能优化：简化图标浮动动画 */
@keyframes iconFloat {
  0%, 100% {
    transform: translate3d(0, 0, 0) rotate(0deg) scale(1);
    opacity: 0.15;
  }
  50% {
    transform: translate3d(15px, -20px, 0) rotate(180deg) scale(1.05); /* 简化动画 */
    opacity: 0.25;
  }
}

/* Energy Beams */
.key-features-section .energy-beams {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

.key-features-section .beam {
  position: absolute;
  background: linear-gradient(45deg,
    transparent 0%,
    rgba(66, 165, 245, 0.1) 30%,
    rgba(255, 149, 0, 0.15) 50%,
    rgba(255, 95, 109, 0.1) 70%,
    transparent 100%);
  filter: blur(2px);
  opacity: 0;
  animation: beamPulse 8s infinite ease-in-out;
}

.key-features-section .beam-1 {
  width: 300px;
  height: 2px;
  top: 30%;
  left: 10%;
  transform: rotate(25deg);
  animation-delay: 0s;
}

.key-features-section .beam-2 {
  width: 250px;
  height: 2px;
  bottom: 25%;
  right: 15%;
  transform: rotate(-35deg);
  animation-delay: 4s;
}

@keyframes beamPulse {
  0%, 100% {
    opacity: 0;
    transform: scale(1) rotate(25deg);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1) rotate(25deg);
  }
}

/* Responsive adjustments for dynamic elements */
@media screen and (max-width: 768px) {
  .key-features-section .print-icon {
    font-size: 18px;
    opacity: 0.2;
  }

  .key-features-section .circuit {
    opacity: 0.5;
  }

  .key-features-section .beam {
    opacity: 0.3;
  }
}

/* Scroll lock visual indicator */
body.scroll-locked {
  user-select: none;
}

body.scroll-locked::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.02);
  z-index: 9999;
  pointer-events: none;
}

/* 🔧 移动端滚动锁定禁用：确保移动端不会被意外锁定 */
@media screen and (max-width: 768px) {
  body.scroll-locked {
    position: static !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    width: auto !important;
    overflow: auto !important;
  }

  body.scroll-locked::before {
    display: none !important;
  }

  /* 🔧 移动端触摸优化：确保自然滚动不被干扰 */
  .key-features-section {
    touch-action: pan-y !important; /* 只允许垂直滚动 */
  }

  .key-features-section .feature-showcase {
    touch-action: pan-y !important; /* 确保功能展示区域也允许垂直滚动 */
  }

  .key-features-section .feature-image-container {
    touch-action: pan-y !important; /* 图片容器也允许垂直滚动 */
  }

  .key-features-section .feature-text-container {
    touch-action: pan-y !important; /* 文本容器也允许垂直滚动 */
  }
}

/* ========================================
   Key Features 性能优化通用规则
   ======================================== */

/* 硬件加速优化 */
.key-features-section * {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

/* 减少重绘和回流 */
.key-features-section .shape,
.key-features-section .print-icon,
.key-features-section .printing-particles::before,
.key-features-section .printing-particles::after,
.key-features-section .circuit,
.key-features-section .beam {
  will-change: transform, opacity;
}

/* 移动设备性能优化 */
@media (max-width: 768px) {
  /* 禁用复杂动画 */
  .key-features-section .floating-shapes,
  .key-features-section .printing-particles,
  .key-features-section .tech-circuits,
  .key-features-section .floating-icons,
  .key-features-section .energy-beams {
    display: none;
  }

  /* 简化背景效果 */
  .key-features-section .tech-lines {
    animation: none;
  }

  /* 减少模糊效果 */
  .key-features-section .shape {
    filter: none;
  }
}

/* 低性能设备优化 */
@media (max-width: 480px) {
  .key-features-section .grid-pattern {
    display: none;
  }
}

/* 🖥️ 低高度屏幕适配 - 针对1920*911等低高度分辨率 */
@media screen and (max-height: 950px) and (min-width: 1200px) {
  .key-features-section {
    height: auto !important; /* 强制自适应高度 */
    min-height: 700px; /* 设置合适的最小高度 */
    padding: 20px 0; /* 减少垂直内边距 */
  }

  .key-features-section .title-group {
    margin-bottom: 1rem; /* 大幅减少间距 */
    margin-top: 0 !important; /* 确保移除负边距 */
    padding-top: 0.5rem; /* 最小顶部内边距 */
  }

  .key-features-section .main-title {
    font-size: 34px; /* 适中的标题大小 */
    margin-bottom: 10px; /* 最小间距 */
    line-height: 1.0; /* 最紧凑行高 */
  }

  .key-features-section .subtitle {
    font-size: 15px; /* 适中的副标题 */
    line-height: 1.3; /* 紧凑行高 */
    margin: 0 auto;
    padding: 0 5px; /* 最小内边距 */
  }

  .key-features-section .feature-tags {
    margin-bottom: 1rem; /* 减少间距 */
    margin-top: 0;
  }

  .key-features-section .feature-showcase {
    margin-bottom: 0; /* 移除底部间距 */
  }

  .key-features-section .feature-image-container {
    height: 240px; /* 进一步减小图片容器 */
    margin-bottom: 0.5rem;
  }

  .key-features-section .feature-img.active {
    transform: scale(1.05) translateX(0) translateZ(0) rotateY(0) !important; /* 轻微缩放 */
  }

  .key-features-section .feature-text-container {
    padding: 0.5rem;
  }

  .key-features-section .feature-title {
    font-size: 24px; /* 减小特性标题 */
    margin-bottom: 0.5rem;
  }

  .key-features-section .feature-description {
    font-size: 13px; /* 减小描述字体 */
    line-height: 1.4;
    margin-bottom: 1rem;
  }

  .key-features-section .feature-bullets li {
    font-size: 12px; /* 减小列表字体 */
    margin-bottom: 8px;
  }
}

/* 用户偏好：减少动画 */
@media (prefers-reduced-motion: reduce) {
  .key-features-section * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .key-features-section .floating-shapes,
  .key-features-section .printing-particles,
  .key-features-section .tech-circuits,
  .key-features-section .floating-icons,
  .key-features-section .energy-beams,
  .key-features-section .tech-lines {
    display: none;
  }
}